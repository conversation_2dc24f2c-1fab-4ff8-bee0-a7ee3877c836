import { AppointmentType, Trainer, AppointmentCard } from "./types";

export const mockAppointmentTypes: AppointmentType[] = [
  {
    id: 1,
    name: "Fitness consultation",
    description: "Get personalized fitness advice and workout plans",
    image:
      "https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400&h=300&fit=crop",
    category: "Consultation",
    duration: "60 min",
    price: "$75",
  },
  {
    id: 2,
    name: "Personal training",
    description: "One-on-one training sessions with certified trainers",
    image:
      "https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400&h=300&fit=crop",
    category: "Training",
    duration: "45 min",
    price: "$120",
  },
  {
    id: 3,
    name: "Small group training",
    description:
      "Train with a small group for motivation and cost-effectiveness",
    image:
      "https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400&h=300&fit=crop",
    category: "Group Training",
    duration: "60 min",
    price: "$45",
  },
];

export const mockTrainers: Trainer[] = [
  {
    id: 1,
    firstName: "Mason",
    lastName: "<PERSON>",
    fullName: "<PERSON>",
    image:
      "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=400&fit=crop&crop=face",
    experience: "6 years of experience",
    specialties: ["Core stability", "Aqua aerobics"],
    tags: ["Post-natal", "Therapeutic exercise"],
    description:
      "A form of cardio exercise alternating bouts of intense anaerobic exercise with less intense recovery periods strengthening the core and overall body.",
    rating: 4.8,
    reviewCount: 124,
    isAvailable: true,
  },
  {
    id: 2,
    firstName: "Ava",
    lastName: "Wilson",
    fullName: "Ava Wilson",
    image:
      "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=400&h=400&fit=crop&crop=face",
    experience: "2 years of experience",
    specialties: ["Agility training", "Barre"],
    tags: ["Post-trauma", "Rehabilitative fitness"],
    description:
      "Specialized in rehabilitation and corrective exercise therapy to help clients recover from injuries and improve their overall fitness.",
    rating: 4.9,
    reviewCount: 89,
    isAvailable: false,
  },
  {
    id: 3,
    firstName: "Mason",
    lastName: "Davis",
    fullName: "Mason Davis",
    image: "", // No image - will show initials
    experience: "6 years of experience",
    specialties: ["Core strength", "HIIT"],
    tags: ["Post-injury", "Corrective exercise"],
    description:
      "Expert in post-operative rehabilitation and functional movement patterns to restore optimal body mechanics.",
    rating: 4.7,
    reviewCount: 156,
    isAvailable: true,
  },
  {
    id: 4,
    firstName: "Ava",
    lastName: "Wilson",
    fullName: "Ava Wilson",
    image:
      "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=400&h=400&fit=crop&crop=face",
    experience: "2 years of experience",
    specialties: ["Powerlifting", "CrossFit"],
    tags: ["Post-accident", "Corrective therapy"],
    description:
      "Specialized in rehabilitation and corrective exercise therapy to help clients recover from injuries and improve their overall fitness.",
    rating: 4.9,
    reviewCount: 89,
    isAvailable: false,
  },
  {
    id: 5,
    firstName: "Ava",
    lastName: "Wilson",
    fullName: "Ava Wilson",
    image:
      "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=400&h=400&fit=crop&crop=face",
    experience: "2 years of experience",
    specialties: ["Mobility work", "Post-operative"],
    tags: ["Functional rehabilitation"],
    description:
      "Expert in post-operative rehabilitation and functional movement patterns to restore optimal body mechanics.",
    rating: 4.7,
    reviewCount: 156,
    isAvailable: false,
  },
];

export const mockAppointmentCards: AppointmentCard[] = [
  {
    id: 1,
    appointmentType: mockAppointmentTypes[0], // Fitness consultation
    trainers: [mockTrainers[0]], // Mason Davis
    availableActions: ["trainers", "purchase", "schedule"],
  },
  {
    id: 2,
    appointmentType: mockAppointmentTypes[1], // Personal training
    trainers: mockTrainers, // All trainers
    availableActions: ["trainers", "purchase", "schedule"],
  },
  {
    id: 3,
    appointmentType: mockAppointmentTypes[2], // Small group training
    trainers: [mockTrainers[0], mockTrainers[1]], // Mason Davis and Ava Wilson
    availableActions: ["trainers", "purchase", "schedule"],
  },
];
