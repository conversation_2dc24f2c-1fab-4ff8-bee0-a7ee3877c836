import React from "react";
import { VStack } from "@/components/ui/vstack";
import { HStack } from "@/components/ui/hstack";
import { Text } from "@/components/ui/text";
import {
  Avatar,
  AvatarImage,
  AvatarFallbackText,
} from "@/components/ui/avatar";
import { Button, ButtonText } from "@/components/ui/button";
import { TouchableOpacity, View } from "react-native";
import { router } from "expo-router";
import { Trainer } from "@/data/screens/appointments/types";
import {
  getInitials,
  getRandomColorForInitials,
} from "@/data/common/common.utils";

interface TrainerCardProps extends Trainer {
  onBookPress?: () => void;
  onRequestInfoPress?: () => void;
  showBookButton?: boolean;
  showRequestInfoButton?: boolean;
}

export const TrainerCard: React.FC<TrainerCardProps> = ({
  id,
  firstName,
  lastName,
  fullName,
  image,
  experience,
  specialties,
  tags,
  description,
  rating,
  reviewCount,
  isAvailable,
  onBookPress,
  onRequestInfoPress,
  showBookButton = true,
  showRequestInfoButton = true,
}) => {
  const handleCardPress = () => {
    router.push({
      pathname: "/trainer-details",
      params: { trainerId: id },
    });
  };

  const handleBookPress = (e: any) => {
    e.stopPropagation();
    if (onBookPress) {
      onBookPress();
    } else {
      console.log("Book pressed for trainer:", fullName);
    }
  };

  const handleRequestInfoPress = (e: any) => {
    e.stopPropagation();
    if (onRequestInfoPress) {
      onRequestInfoPress();
    } else {
      router.push("/request-info");
    }
  };

  // Combine all tags and calculate remaining count
  const allTags = [...specialties, ...tags];
  const displayTags = allTags.slice(0, 4);
  const remainingCount = Math.max(0, allTags.length - 4);

  // Generate initials background color for fallback
  const initialsColor = getRandomColorForInitials(fullName);

  return (
    <TouchableOpacity onPress={handleCardPress} activeOpacity={0.7}>
      <View className="bg-white rounded-2xl p-5 border border-background-200 mb-3 shadow-sm">
        <HStack space="md" className="items-start mb-4">
          {/* Avatar - larger size to match design */}
          {image ? (
            <Avatar size="xl" className="w-20 h-20">
              <AvatarImage source={{ uri: image }} alt={fullName} />
              <AvatarFallbackText className="text-lg font-dm-sans-bold">
                {getInitials(fullName)}
              </AvatarFallbackText>
            </Avatar>
          ) : (
            <View
              className="w-20 h-20 rounded-full items-center justify-center"
              style={{ backgroundColor: "#7DD3FC" }}
            >
              <Text className="text-white text-xl font-dm-sans-bold">
                {getInitials(fullName)}
              </Text>
            </View>
          )}

          <VStack className="flex-1 ml-1">
            <HStack className="items-start justify-between mb-1">
              <VStack className="flex-1">
                <Text className="text-[#00697B] font-dm-sans-bold text-lg leading-tight">
                  {fullName}
                </Text>
                <Text className="text-typography-500 text-sm font-dm-sans-regular mt-1">
                  {experience}
                </Text>
              </VStack>

              {/* Action Button */}
              <View className="ml-3">
                {isAvailable ? (
                  <Button
                    variant="solid"
                    size="sm"
                    className="rounded-full bg-[#00697B] px-6 py-2 min-w-[70px]"
                    onPress={handleBookPress}
                  >
                    <ButtonText className="text-white font-dm-sans-medium text-sm">
                      Book
                    </ButtonText>
                  </Button>
                ) : (
                  <Button
                    variant="outline"
                    size="sm"
                    className="rounded-full border-typography-300 px-4 py-2 min-w-[100px]"
                    onPress={handleRequestInfoPress}
                  >
                    <ButtonText className="text-typography-600 font-dm-sans-medium text-sm">
                      Request info
                    </ButtonText>
                  </Button>
                )}
              </View>
            </HStack>
          </VStack>
        </HStack>

        {/* Tags Section */}
        <VStack space="xs">
          {/* First row of tags */}
          <HStack space="xs" className="flex-wrap">
            {displayTags.slice(0, 2).map((tag, index) => (
              <View
                key={index}
                className="bg-background-100 px-3 py-1.5 rounded-full"
              >
                <Text className="text-typography-600 text-xs font-dm-sans-regular">
                  {tag}
                </Text>
              </View>
            ))}
          </HStack>

          {/* Second row of tags */}
          {displayTags.length > 2 && (
            <HStack space="xs" className="flex-wrap">
              {displayTags.slice(2, 4).map((tag, index) => (
                <View
                  key={index + 2}
                  className="bg-background-100 px-3 py-1.5 rounded-full"
                >
                  <Text className="text-typography-600 text-xs font-dm-sans-regular">
                    {tag}
                  </Text>
                </View>
              ))}
              {remainingCount > 0 && (
                <View className="bg-background-100 px-3 py-1.5 rounded-full">
                  <Text className="text-typography-600 text-xs font-dm-sans-regular">
                    +{remainingCount}
                  </Text>
                </View>
              )}
            </HStack>
          )}
        </VStack>
      </View>
    </TouchableOpacity>
  );
};
